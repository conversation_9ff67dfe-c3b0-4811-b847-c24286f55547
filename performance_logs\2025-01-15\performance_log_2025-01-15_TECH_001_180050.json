{"log_id": "PERF_LOG_2025-01-15_TECH_001_180050", "technician_id": "TECH_001", "shift_date": "2025-01-15", "logged_at": "2025-07-23T18:00:50.301490", "summary_data": {"technician_id": "TECH_001", "shift_date": "2025-01-15", "generated_at": "2025-07-23T18:00:50.268435", "dashboard_analytics": {"month": "July", "day": "23", "week": "Week 30/52", "year": 2025, "total_device_tests_month": 0, "total_device_tests_week": 0, "total_device_tests_day": 0, "total_device_tests_year": 11, "total_manual_tests_month": 1, "total_manual_tests_week": 0, "total_manual_tests_day": 0, "total_manual_tests_year": 1, "total_tests_in_centre": {"glucose": 1, "heart_rate": 0, "blood_pressure": 0, "temperature": 0, "ecg": 0, "lung_capacity": 0, "weight": 0}, "total_tests_count_in_centre": 1, "total_patients_in_centre": 1, "total_patients_today": 0, "per_page": 10, "total_results": 1, "unique_locations": 1, "test_locations": ["Main Lab"], "turnaround_time_stats": {}, "appointments_data": {"per_page": 10, "total_results": 1, "total_appointments_month": 1, "total_appointments_week": 0, "total_appointments_today": 0, "total_appointments_year": 10, "appointment_types": {"physical_appointments": 0, "online_appointments": 1, "total_appointments": 1}, "appointment_status": {"completed": 0, "cancelled": 0, "pending": 1, "completion_rate": 85.0}, "appointment_categories": {"consultation": 0, "follow_up": 0, "lab_review": 0, "routine_checkup": 0, "emergency": 0, "other": 0}, "time_slot_distribution": {"morning": 0, "afternoon": 0, "evening": 0}, "provider_distribution": {"dr_smith": 0, "dr_johnson": 0, "dr_williams": 0, "nurse_practitioner": 0}, "unique_patients_with_appointments": 1, "average_appointments_per_patient": 1.0, "booking_metrics": {"advance_booking_days": 7.5, "same_day_bookings": 0, "no_show_rate": 5.0}}, "medical_staff_data": {"message": "Success", "total_medical_staff": 5, "medical_staff": [{"title": "Dr.", "first_name": "O", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "**********", "id": "7ab01902-68c9-4f20-bb2d-28dbf0c029ae", "specialization": ["General Medicine"], "is_online": true, "last_login": "2025-01-15T08:00:00", "total_patients_registered": 6, "total_tests_done": 29, "type": "MedicalStaffType"}, {"title": "Sir", "first_name": "<PERSON><PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "phone_number": "**********", "id": "a89b3f50-0f30-4973-a4e5-c0181887be3a", "specialization": ["Lab Technology"], "is_online": false, "last_login": "2025-07-10T20:03:27", "total_patients_registered": 0, "total_tests_done": 4, "type": "MedicalStaffType"}, {"title": "", "first_name": "Victor", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone_number": "**********", "id": "a9d6de08-efe7-4908-bb43-027e946cfff2", "specialization": ["Nursing"], "is_online": false, "last_login": "2025-07-22T23:08:48", "total_patients_registered": 0, "total_tests_done": 0, "type": "MedicalStaffType"}, {"title": "", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone_number": "09034235627", "id": "1d21a6d0-0857-44a7-9e86-d57be34678af", "specialization": ["Lab Assistant"], "is_online": false, "last_login": null, "total_patients_registered": 0, "total_tests_done": 0, "type": "MedicalStaffType"}, {"title": "Mr.", "first_name": "<PERSON><PERSON>", "last_name": "Loba", "email": "<EMAIL>", "phone_number": "090464980", "country_code": "+234", "id": "e08c075d-e438-4b65-a190-1853e48c065c", "specialization": ["Doctor"], "preferred_mode_of_consultation": ["video"], "language": "Spanish", "second_language": "Yoruba", "third_language": "French", "is_online": true, "last_login": "2025-07-23T12:09:59", "total_patients_registered": 1, "total_tests_done": 46, "type": "MedicalStaffType"}], "staff_analytics": {"total_staff": 5, "online_staff": 2, "offline_staff": 3, "active_staff": 4, "inactive_staff": 1, "total_patients_handled": 7, "total_tests_conducted": 79}, "specialization_distribution": {"General Medicine": 1, "Lab Technology": 1, "Nursing": 1, "Lab Assistant": 1, "Doctor": 1}, "performance_metrics": {"average_tests_per_staff": 15.8, "average_patients_per_staff": 1.4, "staff_utilization_rate": 40.0}, "top_performers": [{"name": "Mr. <PERSON><PERSON>", "email": "<EMAIL>", "tests_done": 46, "patients_registered": 1, "specialization": "Doctor"}, {"name": "Dr. <PERSON>", "email": "<EMAIL>", "tests_done": 29, "patients_registered": 6, "specialization": "General Medicine"}, {"name": "<PERSON> <PERSON><PERSON>", "email": "<EMAIL>", "tests_done": 4, "patients_registered": 0, "specialization": "Lab Technology"}]}}, "dashboard_insights": {"analysis_period": "monthly", "key_insights": ["Processed 1 total tests in July", "Served 1 patients in the centre", "Conducted 1 manual tests this month", "Most common test type: glucose (1 tests)", "Managed 1 appointments this month", "Appointment completion rate: 85.0%", "Appointment mix: 0 physical, 1 online", "Medical team: 5 staff members", "Currently online: 2 staff members", "Total tests conducted by staff: 79"], "recommendations": []}, "task_analytics": {"total_tasks": 7, "task_type_distribution": {"shift_start": 1, "quality_control": 1, "test_processing": 3, "maintenance": 1, "shift_end": 1}, "flagged_items_count": 1, "flagged_items": [{"description": "Routine maintenance on hematology analyzer", "reason": "Maintenance overdue - completed successfully"}], "duration_stats": {"total_minutes": 210, "average_minutes": 30, "total_hours": 3.5}, "test_processing_count": 3, "unique_tests_processed": 0}, "lab_entries": ["test_name='Blood Glucose' test_date='2025-01-15' test_result=95.5 patient_id='PAT_001' location='Main Lab' test_unit='mg/dL'"], "total_lab_tests": 1, "total_task_activities": 7}, "performance_metrics": {"total_tasks": 0, "flagged_items": 0, "completion_rate": 0, "quality_score": 0}}