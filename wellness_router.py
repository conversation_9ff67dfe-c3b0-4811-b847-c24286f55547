# wellness_center/wellness_router.py

from fastapi import APIRouter
from typing import Dict, Any, List, Union
from pydantic import BaseModel

# --- Import AI Tools ---
from wellness_center.tools_lab_insights_dashboard import (
    lab_insights_dashboard_tool,
    ManualDashboardRequest
)
# Removed lab_activity_statistics imports - tool removed
# Removed technician_performance imports - tool removed
# Removed followup_test_recommender imports - tool removed
from wellness_center.tools_auto_test_explainer import (
    auto_test_explainer_tool,
    AutoTestExplainerRequest
)
# Removed test_data_consistency_checker imports - tool removed
# Removed lab_assistant_reporter imports - lab assistant endpoint removed
from wellness_center.tools_lab_task_summarizer import (
    lab_task_summarizer_tool,
    TaskSummaryRequest
)
# Removed unused tool imports - no corresponding endpoints active



router = APIRouter()

# --- Request Schemas ---

# Removed unused request schemas - no corresponding active endpoints



# --- Endpoints ---

# TODO: Uncomment when tools are created
# @router.post("/wellness/clinical-dashboard")
# async def generate_clinical_dashboard(request: ClinicalDashboardRequest):
#     return generate_clinical_dashboard_insight(request.data)

# @router.get("/wellness/dashboard-live")
# async def dashboard_from_saved_file():
#     with open("data/health_user_data.json", "r") as f:
#         user_data = json.load(f)
#     return generate_clinical_dashboard_insight(user_data)

# TODO: Uncomment when tools are created
# @router.post("/wellness/patient-trends")
# def get_patient_vital_trends(request: PatientVitalTrendsRequest):
#     return analyze_vital_trends(request.patient_id, request.patient_data)

# @router.post("/wellness/doctor-summary")
# def doctor_summary_endpoint(request: DoctorSummaryRequest):
#     return generate_doctor_summary_v2(request.patient_id, request.records)

# @router.post("/wellness/differential-diagnosis")
# def get_differential_diagnosis(request: DifferentialDiagnosisRequest):
#     return differential_diagnosis(vitals=request.vitals, symptoms=request.symptoms, history=request.history)

# @router.post("/wellness/discharge-summary")
# def discharge_summary_endpoint(request: DischargeRequest):
#     return generate_discharge_summary(request.dict())

# @router.post("/wellness/mental-health-screener")
# def run_mental_health_screener(request: MentalHealthRequest):
#     return mental_health_screener(request.phq9, request.gad7, request.history)

# @router.post("/wellness/unstable-detector")
# def detect_unstable_patients(request: PatientSeriesRequest):
#     return unstable_patient_detector(request.data)



@router.post("/wellness/lab-insights-dashboard-manual")
async def lab_insights_dashboard_manual_endpoint(request: ManualDashboardRequest):
    """
    Generate Lab Insights Dashboard using manual data input with validation

    Provides analytics on manually entered lab data with:
    - Data validation and quality checks
    - Top 10 most requested tests
    - Average turnaround time
    - Disease trend indicators
    - Geographic test distribution
    - Insights and recommendations
    - Validation results and warnings
    """
    try:
        # Use the tool to generate dashboard with manual input
        dashboard_result = lab_insights_dashboard_tool.generate_dashboard_with_manual_input(request)

        # Return the dashboard result directly (no extra wrapping)
        return dashboard_result

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to generate manual lab insights dashboard"
        }

# Removed lab-activity-statistics endpoint - tool removed

# Removed technician-performance endpoint - tool removed

# Removed followup-test-recommendations endpoint - tool removed

@router.post("/wellness/auto-test-explainer")
async def auto_test_explainer_endpoint(request: AutoTestExplainerRequest):
    """
    Auto Test Result Explainer for Internal QA (Simplified)

    Analyzes uploaded test results with minimal input required:

    **What it does:**
    - Flags inconsistent entries and data quality issues
    - Identifies outliers based on patient demographics
    - Provides confidence scores to alert technicians
    - Detects technical errors and missing critical data
    - Generates QA recommendations and review requirements
    - Calculates overall quality score for result validation

    **Simplified input required:**
    - Just provide technician_id, basic patient demographics, and test results
    - Auto-generates timestamps, priority levels, and quality check levels
    - Minimal patient demographics: patient_id, age, sex only
    - Simplified test results: test_name, test_value, reference_range, units only

    **Required fields:** technician_id, patient_demographics (basic), test_results only!
    """
    try:
        # Use the tool to analyze test results for quality control
        result = auto_test_explainer_tool.analyze_test_results(request)

        # Return the result directly
        return result

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to analyze test results for quality control"
        }

@router.post("/wellness/lab-task-summary")
async def lab_task_summary_endpoint(request: TaskSummaryRequest):
    """
    Lab Task Summarizer Bot - End of Shift Summary (All-in-One + Document Export)

    Creates comprehensive end-of-shift task summaries with multiple export formats:

    **What it does:**
    - Counts completed tests and procedures ("You completed 42 tests today. 3 were flagged for review.")
    - Identifies flagged items requiring review with detailed reasons
    - Generates performance metrics and ratings
    - Creates handover summaries for next shift
    - **Automatically saves performance logs to files** for audits and team handovers
    - **Exports to multiple formats**: JSON, PDF, DOCX

    **Minimal input required:**
    - Just provide technician_id (everything else is auto-generated)
    - Optionally specify shift_date, summary_format, and export_formats
    - Automatically generates shift times (8-hour shifts)
    - Provides brief, standard, or detailed summary formats

    **Export formats available:**
    - PDF (default): Professional formatted document
    - DOCX: Microsoft Word document
    - JSON: Raw data format

    **Files saved to:**
    - Performance logs: performance_logs/YYYY-MM-DD/PERF_LOG_*.json
    - Task summaries: task_summary_exports/YYYY-MM-DD/TASK_SUMMARY_*.pdf (default)

    **Required fields:** technician_id only!
    """
    try:
        # Use the tool to generate task summary
        result = lab_task_summarizer_tool.generate_task_summary(request)

        # Return the result directly
        return result

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to generate task summary"
        }

# Removed lab-performance-log endpoint - performance logs are now automatically saved when generating task summaries


    