# wellness_center/wellness_router.py

from fastapi import APIRouter
from typing import Dict, Any, List, Union
from pydantic import BaseModel

# --- Import AI Tools ---
from wellness_center.tools_unified_lab_manager import (
    UnifiedLabManagerTool,
    UnifiedLabRequest
)
from wellness_center.tools_auto_test_explainer import (
    auto_test_explainer_tool,
    AutoTestExplainerRequest
)
# Removed unused tool imports - consolidated into unified lab manager



router = APIRouter()

# Create unified tool instance
unified_lab_manager_tool = UnifiedLabManagerTool()

# --- Request Schemas ---

# Removed unused request schemas - no corresponding active endpoints



# --- Endpoints ---

# TODO: Uncomment when tools are created
# @router.post("/wellness/clinical-dashboard")
# async def generate_clinical_dashboard(request: ClinicalDashboardRequest):
#     return generate_clinical_dashboard_insight(request.data)

# @router.get("/wellness/dashboard-live")
# async def dashboard_from_saved_file():
#     with open("data/health_user_data.json", "r") as f:
#         user_data = json.load(f)
#     return generate_clinical_dashboard_insight(user_data)

# TODO: Uncomment when tools are created
# @router.post("/wellness/patient-trends")
# def get_patient_vital_trends(request: PatientVitalTrendsRequest):
#     return analyze_vital_trends(request.patient_id, request.patient_data)

# @router.post("/wellness/doctor-summary")
# def doctor_summary_endpoint(request: DoctorSummaryRequest):
#     return generate_doctor_summary_v2(request.patient_id, request.records)

# @router.post("/wellness/differential-diagnosis")
# def get_differential_diagnosis(request: DifferentialDiagnosisRequest):
#     return differential_diagnosis(vitals=request.vitals, symptoms=request.symptoms, history=request.history)

# @router.post("/wellness/discharge-summary")
# def discharge_summary_endpoint(request: DischargeRequest):
#     return generate_discharge_summary(request.dict())

# @router.post("/wellness/mental-health-screener")
# def run_mental_health_screener(request: MentalHealthRequest):
#     return mental_health_screener(request.phq9, request.gad7, request.history)

# @router.post("/wellness/unstable-detector")
# def detect_unstable_patients(request: PatientSeriesRequest):
#     return unstable_patient_detector(request.data)



@router.post("/wellness/unified-lab-manager")
async def unified_lab_manager_endpoint(request: UnifiedLabRequest):
    """
    Unified Lab Manager - Dashboard & Task Summary (All-in-One)

    Handles both lab insights dashboard and task summary functionality:

    **Dashboard Mode** (request_type: "dashboard"):
    - Manual lab data analytics with validation
    - Data validation and quality checks
    - Top 10 most requested tests and trends
    - Average turnaround time analysis
    - Disease trend indicators
    - Geographic test distribution
    - Insights and recommendations

    **Task Summary Mode** (request_type: "task_summary"):
    - End-of-shift task summaries with multiple export formats
    - Performance analytics and flagged items
    - Handover information and recommendations
    - Automatic performance log saving
    - PDF, DOCX, and JSON export options

    **Input:** request_type ("dashboard" or "task_summary"), technician_id, plus mode-specific fields

    **Output:** Comprehensive results with analytics, summaries, and document exports

    Perfect unified solution for all lab management needs!
    """
    try:
        # Use the unified tool to process the request
        result = unified_lab_manager_tool.process_unified_request(request)
        return result

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to process unified lab manager request"
        }


@router.post("/wellness/auto-test-explainer")
async def auto_test_explainer_endpoint(request: AutoTestExplainerRequest):
    """
    Auto Test Result Explainer for Internal QA (Simplified)

    Analyzes uploaded test results with minimal input required:

    **What it does:**
    - Flags inconsistent entries and data quality issues
    - Identifies outliers based on patient demographics
    - Provides confidence scores to alert technicians
    - Detects technical errors and missing critical data
    - Generates QA recommendations and review requirements
    - Calculates overall quality score for result validation

    **Simplified input required:**
    - Just provide technician_id, basic patient demographics, and test results
    - Auto-generates timestamps, priority levels, and quality check levels
    - Minimal patient demographics: patient_id, age, sex only
    - Simplified test results: test_name, test_value, reference_range, units only

    **Required fields:** technician_id, patient_demographics (basic), test_results only!
    """
    try:
        # Use the tool to analyze test results for quality control
        result = auto_test_explainer_tool.analyze_test_results(request)

        # Return the result directly
        return result

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Failed to analyze test results for quality control"
        }

# Removed lab-task-summary endpoint - merged into unified-lab-manager endpoint
 