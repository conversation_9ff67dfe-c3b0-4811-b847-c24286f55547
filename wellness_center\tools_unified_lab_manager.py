"""
Unified Lab Manager Tool

Combines lab insights dashboard and task summary functionality into a single tool:
- Lab insights dashboard with manual data input and validation
- End-of-shift task summaries with PDF export
- Performance analytics and trend analysis
- Document generation and export capabilities
"""

import json
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from collections import Counter, defaultdict
from pydantic import BaseModel, Field, field_validator
from langchain.tools import Tool
import statistics

# Document generation imports
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Request Models ---

class ManualLabDataEntry(BaseModel):
    """Manual lab data entry for dashboard analytics"""
    test_name: str
    test_date: str  # YYYY-MM-DD format
    test_result: Union[str, float, int, Dict[str, Any]]  # Support complex test results
    patient_id: str
    location: str
    test_unit: Optional[str] = None
    turnaround_time_hours: Optional[float] = None

    @field_validator('test_date')
    @classmethod
    def validate_date(cls, v):
        datetime.strptime(v, '%Y-%m-%d')  # Validates format
        return v

class TaskActivity(BaseModel):
    """Individual task activity record for summaries"""
    task_id: str
    task_type: str  # "test_processing", "quality_control", "sample_handling", "calibration", "maintenance"
    test_name: Optional[str] = None
    sample_id: Optional[str] = None
    completion_time: str  # ISO format timestamp
    duration_minutes: Optional[int] = None
    technician_id: str
    location: str = "main_lab"
    notes: Optional[str] = None
    flag_reason: Optional[str] = None  # Reason if flagged for review

class UnifiedLabRequest(BaseModel):
    """Unified request model for both dashboard and task summary functionality"""
    # Common fields
    technician_id: str
    request_type: str  # "dashboard", "task_summary", "combined"
    
    # Dashboard-specific fields
    admin_id: Optional[str] = None
    date_range_days: int = 30
    analysis_period: str = "monthly"  # daily, weekly, monthly, quarterly, yearly
    manual_lab_entries: List[ManualLabDataEntry] = []
    validation_enabled: bool = True
    test_type_filter: Optional[str] = None
    
    # Task summary-specific fields
    shift_date: Optional[str] = None  # Auto-generated if not provided (today's date)
    summary_format: str = "standard"  # "brief", "standard", "detailed"
    export_formats: List[str] = ["pdf"]  # "pdf", "docx", "json"
    task_activities: List[TaskActivity] = []
    include_performance_log: bool = True

class UnifiedLabManagerTool:
    """Unified tool combining lab insights dashboard and task summary functionality"""
    
    def __init__(self):
        self.name = "unified_lab_manager"
        self.description = "Unified lab management tool for dashboards and task summaries"
        self.logger = logging.getLogger(__name__)
        
        # Ensure directories exist
        os.makedirs("data/lab_reports", exist_ok=True)
        os.makedirs("data/performance_logs", exist_ok=True)
    
    def process_unified_request(self, request: UnifiedLabRequest) -> Dict[str, Any]:
        """Main method to process unified lab management requests"""
        try:
            self.logger.info(f"Processing unified lab request: {request.request_type}")
            
            if request.request_type == "dashboard":
                return self._generate_dashboard(request)
            elif request.request_type == "task_summary":
                return self._generate_task_summary(request)
            elif request.request_type == "combined":
                return self._generate_combined_report(request)
            else:
                return {
                    "success": False,
                    "error": f"Invalid request_type: {request.request_type}",
                    "message": "Supported types: dashboard, task_summary, combined"
                }
                
        except Exception as e:
            self.logger.error(f"Error processing unified request: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to process unified lab request"
            }
    
    def _generate_dashboard(self, request: UnifiedLabRequest) -> Dict[str, Any]:
        """Generate lab insights dashboard"""
        try:
            # Validate manual entries if validation is enabled
            if request.validation_enabled:
                validation_results = self._validate_lab_entries(request.manual_lab_entries)
            else:
                validation_results = {"valid_entries": len(request.manual_lab_entries), "errors": []}
            
            # Generate analytics
            analytics = self._generate_lab_analytics(request.manual_lab_entries, request)
            
            # Generate insights
            insights = self._generate_insights(analytics, request.analysis_period)
            
            return {
                "success": True,
                "request_type": "dashboard",
                "admin_id": request.admin_id,
                "technician_id": request.technician_id,
                "analysis_period": request.analysis_period,
                "date_range_days": request.date_range_days,
                "validation_results": validation_results,
                "analytics": analytics,
                "insights": insights,
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error generating dashboard: {str(e)}")
            raise
    
    def _generate_task_summary(self, request: UnifiedLabRequest) -> Dict[str, Any]:
        """Generate end-of-shift task summary"""
        try:
            # Use provided date or default to today
            shift_date = request.shift_date or datetime.now().strftime('%Y-%m-%d')
            
            # Generate task analytics
            task_analytics = self._analyze_task_activities(request.task_activities)
            
            # Generate summary based on format
            summary = self._create_task_summary(task_analytics, request.summary_format, shift_date)
            
            # Generate documents if requested
            documents = {}
            if "pdf" in request.export_formats and PDF_AVAILABLE:
                pdf_path = self._generate_pdf_summary(summary, request.technician_id, shift_date)
                documents["pdf"] = pdf_path
            
            if "docx" in request.export_formats and DOCX_AVAILABLE:
                docx_path = self._generate_docx_summary(summary, request.technician_id, shift_date)
                documents["docx"] = docx_path
            
            if "json" in request.export_formats:
                json_path = self._save_json_summary(summary, request.technician_id, shift_date)
                documents["json"] = json_path
            
            # Save performance log if requested
            performance_log = None
            if request.include_performance_log:
                performance_log = self._save_performance_log(task_analytics, request.technician_id, shift_date)
            
            return {
                "success": True,
                "request_type": "task_summary",
                "technician_id": request.technician_id,
                "shift_date": shift_date,
                "summary_format": request.summary_format,
                "task_analytics": task_analytics,
                "summary": summary,
                "documents": documents,
                "performance_log": performance_log,
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error generating task summary: {str(e)}")
            raise
    
    def _generate_combined_report(self, request: UnifiedLabRequest) -> Dict[str, Any]:
        """Generate combined dashboard and task summary report"""
        try:
            # Generate both dashboard and task summary
            dashboard_result = self._generate_dashboard(request)
            task_summary_result = self._generate_task_summary(request)
            
            # Combine results
            combined_result = {
                "success": True,
                "request_type": "combined",
                "technician_id": request.technician_id,
                "dashboard": dashboard_result,
                "task_summary": task_summary_result,
                "generated_at": datetime.now().isoformat()
            }
            
            return combined_result
            
        except Exception as e:
            self.logger.error(f"Error generating combined report: {str(e)}")
            raise

    def _validate_lab_entries(self, entries: List[ManualLabDataEntry]) -> Dict[str, Any]:
        """Validate manual lab entries"""
        errors = []
        valid_entries = 0

        for i, entry in enumerate(entries):
            try:
                # Validate date format
                datetime.strptime(entry.test_date, '%Y-%m-%d')

                # Validate required fields
                if not entry.test_name or not entry.patient_id:
                    errors.append(f"Entry {i+1}: Missing required fields")
                    continue

                # Validate test result
                if entry.test_result is None or entry.test_result == "":
                    errors.append(f"Entry {i+1}: Missing test result")
                    continue

                valid_entries += 1

            except ValueError as e:
                errors.append(f"Entry {i+1}: Invalid date format - {str(e)}")
            except Exception as e:
                errors.append(f"Entry {i+1}: Validation error - {str(e)}")

        return {
            "total_entries": len(entries),
            "valid_entries": valid_entries,
            "invalid_entries": len(entries) - valid_entries,
            "errors": errors,
            "validation_passed": len(errors) == 0
        }

    def _generate_lab_analytics(self, entries: List[ManualLabDataEntry], request: UnifiedLabRequest) -> Dict[str, Any]:
        """Generate analytics from lab entries"""
        if not entries:
            return {"message": "No lab entries provided", "analytics": {}}

        # Basic counts
        total_tests = len(entries)
        unique_patients = len(set(entry.patient_id for entry in entries))
        unique_locations = len(set(entry.location for entry in entries))

        # Test type analysis
        test_counts = Counter(entry.test_name for entry in entries)
        most_common_tests = test_counts.most_common(5)

        # Location analysis
        location_counts = Counter(entry.location for entry in entries)

        # Date analysis
        date_counts = Counter(entry.test_date for entry in entries)

        # Turnaround time analysis
        turnaround_times = [entry.turnaround_time_hours for entry in entries if entry.turnaround_time_hours is not None]
        turnaround_stats = {}
        if turnaround_times:
            turnaround_stats = {
                "average": round(statistics.mean(turnaround_times), 2),
                "median": round(statistics.median(turnaround_times), 2),
                "min": min(turnaround_times),
                "max": max(turnaround_times)
            }

        return {
            "total_tests": total_tests,
            "unique_patients": unique_patients,
            "unique_locations": unique_locations,
            "test_type_distribution": dict(test_counts),
            "most_common_tests": most_common_tests,
            "location_distribution": dict(location_counts),
            "daily_test_counts": dict(date_counts),
            "turnaround_time_stats": turnaround_stats
        }

    def _generate_insights(self, analytics: Dict[str, Any], analysis_period: str) -> Dict[str, Any]:
        """Generate insights from analytics"""
        insights = []

        if analytics.get("total_tests", 0) > 0:
            insights.append(f"Processed {analytics['total_tests']} tests across {analytics['unique_locations']} locations")
            insights.append(f"Served {analytics['unique_patients']} unique patients")

            if analytics.get("most_common_tests"):
                top_test = analytics["most_common_tests"][0]
                insights.append(f"Most frequent test: {top_test[0]} ({top_test[1]} times)")

            if analytics.get("turnaround_time_stats"):
                avg_time = analytics["turnaround_time_stats"]["average"]
                insights.append(f"Average turnaround time: {avg_time} hours")

        return {
            "analysis_period": analysis_period,
            "key_insights": insights,
            "recommendations": self._generate_recommendations(analytics)
        }

    def _generate_recommendations(self, analytics: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on analytics"""
        recommendations = []

        if analytics.get("turnaround_time_stats"):
            avg_time = analytics["turnaround_time_stats"]["average"]
            if avg_time > 24:
                recommendations.append("Consider optimizing workflow to reduce turnaround times")

        if analytics.get("location_distribution"):
            locations = analytics["location_distribution"]
            if len(locations) > 1:
                recommendations.append("Monitor workload distribution across locations")

        return recommendations

    def _analyze_task_activities(self, activities: List[TaskActivity]) -> Dict[str, Any]:
        """Analyze task activities for summary generation"""
        if not activities:
            return {"message": "No task activities provided", "analytics": {}}

        # Basic counts
        total_tasks = len(activities)
        task_type_counts = Counter(activity.task_type for activity in activities)

        # Flagged items
        flagged_items = [activity for activity in activities if activity.flag_reason]

        # Duration analysis
        durations = [activity.duration_minutes for activity in activities if activity.duration_minutes is not None]
        duration_stats = {}
        if durations:
            duration_stats = {
                "total_minutes": sum(durations),
                "average_minutes": round(statistics.mean(durations), 2),
                "total_hours": round(sum(durations) / 60, 2)
            }

        # Test processing analysis
        test_activities = [activity for activity in activities if activity.task_type == "test_processing"]
        unique_tests = len(set(activity.test_name for activity in test_activities if activity.test_name))

        return {
            "total_tasks": total_tasks,
            "task_type_distribution": dict(task_type_counts),
            "flagged_items_count": len(flagged_items),
            "flagged_items": [{"task_id": item.task_id, "reason": item.flag_reason} for item in flagged_items],
            "duration_stats": duration_stats,
            "test_processing_count": len(test_activities),
            "unique_tests_processed": unique_tests
        }

    def _create_task_summary(self, analytics: Dict[str, Any], format_type: str, shift_date: str) -> Dict[str, Any]:
        """Create task summary based on format type"""
        base_summary = {
            "shift_date": shift_date,
            "total_tasks_completed": analytics.get("total_tasks", 0),
            "flagged_items": analytics.get("flagged_items_count", 0),
            "total_work_hours": analytics.get("duration_stats", {}).get("total_hours", 0)
        }

        if format_type == "brief":
            return base_summary
        elif format_type == "detailed":
            base_summary.update({
                "task_breakdown": analytics.get("task_type_distribution", {}),
                "flagged_details": analytics.get("flagged_items", []),
                "performance_metrics": analytics.get("duration_stats", {}),
                "tests_processed": analytics.get("unique_tests_processed", 0)
            })

        return base_summary
